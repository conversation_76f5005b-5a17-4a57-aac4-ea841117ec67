# Prometheus Metrics Implementation for AgentQ Backend

## Overview

This document describes the comprehensive Prometheus metrics implementation for the AgentQ backend NestJS application. The implementation provides detailed monitoring capabilities for HTTP traffic, database operations, system resources, and business metrics.

## Features Implemented

### 1. HTTP Request Metrics
- **Request Count**: Total number of HTTP requests by method, route, and status code
- **Request Duration**: Response time histograms with configurable buckets
- **Requests In Progress**: Current number of active requests being processed
- **Route Normalization**: Automatic normalization of dynamic routes (UUIDs, IDs) for better grouping

### 2. Database Metrics
- **Connection Pool**: Active and idle database connections
- **Query Performance**: Query duration and success/error rates
- **Cache Hit Ratio**: Database cache performance metrics
- **Connection Health**: Real-time database connection monitoring

### 3. System Metrics
- **Memory Usage**: Heap usage, RSS, and total memory consumption
- **CPU Usage**: System and process CPU utilization
- **Application Uptime**: Service availability tracking
- **Node.js Metrics**: Event loop lag, garbage collection, heap spaces

### 4. Business Metrics
- **Users**: Total user count
- **Projects**: Total project count
- **Test Cases**: Count by project
- **API Keys**: Count by status
- **Test Runs**: Counter with status and project labels
- **Issues**: Count by status and severity

## Architecture

### Core Components

1. **MetricsService** (`src/metrics/metrics.service.ts`)
   - Central service managing all Prometheus metrics
   - Provides helper methods for recording metrics
   - Handles metric initialization and configuration

2. **HttpMetricsMiddleware** (`src/metrics/http-metrics.middleware.ts`)
   - Automatically captures HTTP request metrics
   - Route normalization for better aggregation
   - Request duration and status code tracking

3. **DatabaseMetricsService** (`src/metrics/database-metrics.service.ts`)
   - Collects database connection pool metrics
   - Monitors query performance
   - Tracks cache hit ratios

4. **BusinessMetricsService** (`src/metrics/business-metrics.service.ts`)
   - Collects application-specific metrics
   - Updates business KPIs every 60 seconds
   - Tracks user, project, and test case counts

5. **SystemMetricsIntegrationService** (`src/metrics/system-metrics-integration.service.ts`)
   - Integrates with existing health monitoring
   - Updates system resource metrics every 15 seconds

### Endpoints

- **`/metrics`**: Main Prometheus metrics endpoint (Prometheus format)
- **`/metrics/health`**: Health check for metrics service (JSON format)

## Metrics Available

### HTTP Metrics
```
http_requests_total{method="GET",route="/health",status_code="200"} 1
http_request_duration_seconds{method="GET",route="/health",status_code="200"} 0.045
http_requests_in_progress{method="GET",route="/health"} 0
```

### Database Metrics
```
database_connections_active 1
database_connections_idle 1
database_query_duration_seconds{operation="SELECT",table="users"} 0.002
database_queries_total{operation="SELECT",table="users",status="success"} 5
database_cache_hit_ratio 99.97
```

### System Metrics
```
memory_usage_bytes{type="heap_used"} 62054727
memory_usage_bytes{type="heap_total"} 66270003
memory_usage_bytes{type="rss"} 129027276
cpu_usage_percent{type="system"} 0.03
cpu_usage_percent{type="process"} 0.42
application_uptime_seconds 120.5
```

### Business Metrics
```
users_total 4
projects_total 6
test_cases_total{project_id="f907b2b1-4347-480c-8bc5-0b669649599a"} 84
api_keys_total{status="active"} 3
test_runs_total{status="completed",project_id="abc123"} 15
issues_total{status="open",severity="high"} 2
```

## Configuration

### Metric Collection Intervals
- **Database Metrics**: Every 30 seconds
- **Business Metrics**: Every 60 seconds  
- **System Metrics**: Every 15 seconds
- **HTTP Metrics**: Real-time (per request)

### Histogram Buckets
- **HTTP Request Duration**: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10] seconds
- **Database Query Duration**: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5] seconds

## Integration with Prometheus

### Prometheus Configuration
Add the following to your `prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'agentq-backend'
    static_configs:
      - targets: ['localhost:3010']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

### Grafana Dashboard Queries

#### HTTP Request Rate
```promql
rate(http_requests_total[5m])
```

#### Average Response Time
```promql
rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])
```

#### Database Connection Pool Usage
```promql
database_connections_active / (database_connections_active + database_connections_idle) * 100
```

#### Memory Usage Percentage
```promql
memory_usage_bytes{type="heap_used"} / memory_usage_bytes{type="heap_total"} * 100
```

## Usage Examples

### Recording Custom Business Events

```typescript
// In your service
constructor(private metricsService: MetricsService) {}

// Record a test run completion
this.metricsService.recordTestRun('completed', projectId);

// Record a new user registration
this.metricsService.updateUsersCount(newUserCount);
```

### Database Query Monitoring

```typescript
// In your repository
constructor(private databaseMetricsService: DatabaseMetricsService) {}

// Wrap database queries for automatic monitoring
const result = await this.databaseMetricsService.recordQuery(
  'SELECT',
  'users',
  () => this.userRepository.find()
);
```

## Monitoring and Alerting

### Recommended Alerts

1. **High Error Rate**: `rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1`
2. **High Response Time**: `histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1`
3. **Database Connection Issues**: `database_connections_active > 10`
4. **Memory Usage**: `memory_usage_bytes{type="heap_used"} / memory_usage_bytes{type="heap_total"} > 0.9`

## Files Created/Modified

### New Files
- `src/metrics/metrics.service.ts`
- `src/metrics/metrics.controller.ts`
- `src/metrics/metrics.module.ts`
- `src/metrics/http-metrics.middleware.ts`
- `src/metrics/database-metrics.service.ts`
- `src/metrics/business-metrics.service.ts`
- `src/metrics/system-metrics-integration.service.ts`

### Modified Files
- `src/app.module.ts` - Added MetricsModule
- `src/main.ts` - Added HTTP metrics middleware
- `package.json` - Added prom-client dependency

## Next Steps

1. **Configure Prometheus** to scrape the `/metrics` endpoint
2. **Set up Grafana dashboards** using the provided queries
3. **Configure alerting rules** based on your SLA requirements
4. **Monitor and tune** metric collection intervals based on your needs
5. **Add custom business metrics** specific to your application requirements

The implementation provides a solid foundation for comprehensive application monitoring and can be extended with additional custom metrics as needed.
