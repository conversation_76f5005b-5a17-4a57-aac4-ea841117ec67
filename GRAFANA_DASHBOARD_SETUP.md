# Grafana Dashboard Setup for AgentQ Backend

## Overview

This document provides instructions for setting up the Grafana dashboard to monitor your AgentQ backend service using the provided JSON configuration file.

## Dashboard Features

The dashboard includes the following monitoring panels:

### 📊 **HTTP Metrics**
- **HTTP Request Rate**: Real-time request rate by method, route, and status code
- **HTTP Response Time**: 95th and 50th percentile response times

### 📈 **Business Metrics**
- **Total Users**: Current user count
- **Total Projects**: Current project count  
- **Application Uptime**: Service availability time
- **Test Cases by Project**: Test case distribution across projects

### 🗄️ **Database Metrics**
- **Database Connections**: Active vs idle connection monitoring
- **Database Cache Hit Ratio**: Cache performance percentage

### 💾 **System Metrics**
- **Memory Usage %**: Heap memory utilization gauge
- **Memory Usage Details**: Detailed memory breakdown (heap used/total, RSS)
- **CPU Usage**: System and process CPU utilization

## Prerequisites

1. **Prometheus** configured to scrape your AgentQ backend at `/metrics` endpoint
2. **Grafana** instance with Prometheus as a data source
3. Your AgentQ backend service running with metrics enabled

## Installation Steps

### 1. Configure Prometheus

Add this job to your `prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'agentq-backend'
    static_configs:
      - targets: ['your-backend-host:3010']  # Replace with your actual host
    metrics_path: '/metrics'
    scrape_interval: 15s
    static_configs:
      - targets: ['your-backend-host:3010']
        labels:
          app: 'app-backend-agentq'  # This label is crucial for the dashboard
```

**Important**: The `app: 'app-backend-agentq'` label is required for the dashboard to work correctly.

### 2. Import Dashboard to Grafana

#### Method 1: Import via Grafana UI
1. Open Grafana web interface
2. Go to **Dashboards** → **Import**
3. Click **Upload JSON file**
4. Select the `grafana-dashboard-agentq-backend.json` file
5. Configure the data source (select your Prometheus instance)
6. Click **Import**

#### Method 2: Import via JSON
1. Open Grafana web interface
2. Go to **Dashboards** → **Import**
3. Copy and paste the entire content of `grafana-dashboard-agentq-backend.json`
4. Click **Load**
5. Configure the data source (select your Prometheus instance)
6. Click **Import**

### 3. Configure Data Source

If you haven't configured Prometheus as a data source:

1. Go to **Configuration** → **Data Sources**
2. Click **Add data source**
3. Select **Prometheus**
4. Configure the URL (e.g., `http://prometheus:9090`)
5. Click **Save & Test**

## Dashboard Configuration

### Template Variables

The dashboard uses one template variable:

- **App**: Fixed to `app-backend-agentq` (matches the Prometheus label)

This variable filters all metrics to show only data from your AgentQ backend service.

### Time Range

- **Default**: Last 1 hour
- **Refresh**: Every 5 seconds
- You can adjust these settings using Grafana's time picker

## Panel Descriptions

### HTTP Request Rate
- **Query**: `rate(http_requests_total{app="$app"}[5m])`
- **Shows**: Requests per second by endpoint and status code
- **Use**: Monitor traffic patterns and identify high-traffic endpoints

### HTTP Response Time
- **Queries**: 
  - `histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{app="$app"}[5m]))`
  - `histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{app="$app"}[5m]))`
- **Shows**: Response time percentiles
- **Use**: Monitor application performance and identify slow endpoints

### Database Connections
- **Queries**:
  - `database_connections_active{app="$app"}`
  - `database_connections_idle{app="$app"}`
- **Shows**: Database connection pool status
- **Use**: Monitor database connection health and potential bottlenecks

### Memory Usage %
- **Query**: `memory_usage_bytes{app="$app",type="heap_used"} / memory_usage_bytes{app="$app",type="heap_total"} * 100`
- **Shows**: Memory utilization percentage with thresholds
- **Use**: Monitor memory consumption and potential memory leaks

### Test Cases by Project
- **Query**: `sum by (project_id) (test_cases_total{app="$app"})`
- **Shows**: Distribution of test cases across projects
- **Use**: Monitor business metrics and project activity

## Alerting Setup

### Recommended Alerts

You can set up alerts based on these queries:

#### High Error Rate
```promql
rate(http_requests_total{app="app-backend-agentq",status_code=~"5.."}[5m]) > 0.1
```

#### High Response Time
```promql
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{app="app-backend-agentq"}[5m])) > 1
```

#### High Memory Usage
```promql
memory_usage_bytes{app="app-backend-agentq",type="heap_used"} / memory_usage_bytes{app="app-backend-agentq",type="heap_total"} * 100 > 90
```

#### Database Connection Issues
```promql
database_connections_active{app="app-backend-agentq"} > 10
```

### Setting Up Alerts

1. Go to **Alerting** → **Alert Rules**
2. Click **New Rule**
3. Use the queries above as conditions
4. Configure notification channels (email, Slack, etc.)
5. Set appropriate thresholds and evaluation intervals

## Troubleshooting

### No Data Showing

1. **Check Prometheus targets**: Go to Prometheus UI → Status → Targets
2. **Verify labels**: Ensure your service has the `app="app-backend-agentq"` label
3. **Check metrics endpoint**: Visit `http://your-backend:3010/metrics` directly
4. **Verify data source**: Test Prometheus connection in Grafana

### Partial Data

1. **Check scrape interval**: Ensure Prometheus is scraping frequently enough
2. **Verify metric names**: Some metrics might have different names
3. **Check time range**: Adjust the dashboard time range

### Performance Issues

1. **Reduce refresh rate**: Change from 5s to 30s or 1m
2. **Limit time range**: Use shorter time windows for better performance
3. **Optimize queries**: Add rate intervals or reduce cardinality

## Customization

### Adding New Panels

1. Click **Add Panel** in edit mode
2. Use existing queries as templates
3. Adjust visualization type (timeseries, stat, gauge, etc.)
4. Configure thresholds and colors

### Modifying Queries

All queries use the `{app="$app"}` filter. When adding new metrics:
- Include this filter for consistency
- Use appropriate rate() functions for counters
- Consider using histogram_quantile() for latency metrics

## Dashboard JSON Structure

The dashboard includes:
- **11 panels** covering all major metrics
- **Template variable** for app filtering
- **Responsive layout** that works on different screen sizes
- **Consistent styling** with appropriate colors and thresholds

## Support

If you encounter issues:
1. Check the Prometheus metrics endpoint directly
2. Verify the app label is correctly set
3. Ensure all required metrics are being exported
4. Check Grafana logs for any errors

The dashboard is designed to provide comprehensive monitoring of your AgentQ backend service with minimal configuration required.
