{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "sum by (method) (rate(http_requests_total{app=\"$app\"}[5m]))", "legendFormat": "{{method}}", "refId": "A"}], "title": "RPS by method", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "sum by (status_code) (rate(http_requests_total{app=\"$app\"}[5m]))", "legendFormat": "{{status_code}}", "refId": "A"}], "title": "RPS by status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "histogram_quantile(0.5,  sum by (le) (rate(http_request_duration_seconds_bucket{app=\"$app\"}[5m])))", "legendFormat": "p50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "histogram_quantile(0.9,  sum by (le) (rate(http_request_duration_seconds_bucket{app=\"$app\"}[5m])))", "legendFormat": "p90", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "histogram_quantile(0.99, sum by (le) (rate(http_request_duration_seconds_bucket{app=\"$app\"}[5m])))", "legendFormat": "p99", "refId": "C"}], "title": "Latency p50/p90/p99", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "sum(http_requests_in_progress{app=\"$app\"})", "legendFormat": "in-progress", "refId": "A"}], "title": "Active Requests (in progress)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "database_connections_active{app=\"$app\"}", "interval": "", "legendFormat": "Active Connections", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "database_connections_idle{app=\"$app\"}", "interval": "", "legendFormat": "Idle Connections", "refId": "B"}], "title": "Database Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "database_cache_hit_ratio{app=\"$app\"}", "interval": "", "legendFormat": "<PERSON><PERSON>", "refId": "A"}], "title": "Database <PERSON>ache <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "sum by (project_id) (test_cases_total{app=\"$app\"})", "interval": "", "legendFormat": "Project {{project_id}}", "refId": "A"}], "title": "Test Cases by Project", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "memory_usage_bytes{app=\"$app\",type=\"heap_used\"}", "interval": "", "legendFormat": "Heap Used", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "memory_usage_bytes{app=\"$app\",type=\"heap_total\"}", "interval": "", "legendFormat": "Heap Total", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "memory_usage_bytes{app=\"$app\",type=\"rss\"}", "interval": "", "legendFormat": "RSS", "refId": "C"}], "title": "Memory Usage Details", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "cpu_usage_percent{app=\"$app\",type=\"system\"}", "interval": "", "legendFormat": "System CPU", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "expr": "cpu_usage_percent{app=\"$app\",type=\"process\"}", "interval": "", "legendFormat": "Process CPU", "refId": "B"}], "title": "CPU Usage", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 36, "style": "dark", "tags": ["agentq", "backend", "nodejs", "<PERSON><PERSON><PERSON>"], "templating": {"list": [{"current": {"text": "app-backend-agentq", "value": "app-backend-agentq"}, "datasource": {"type": "prometheus", "uid": "eetepllzvuubkf"}, "includeAll": false, "label": "App label", "name": "app", "options": [], "query": "label_values(http_requests_total, app)", "refresh": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "AgentQ Backend Monitoring", "uid": "agentq-backend", "version": 1, "weekStart": ""}