import { config } from 'dotenv';

config();

export const Config = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  MIDTRANS_CLIENT_KEY: process.env.MIDTRANS_CLIENT_KEY!,
  MIDTRANS_SERVER_KEY: process.env.MIDTRANS_SERVER_KEY!,
  CLIENT_URL: process.env.CLIENT_URL!,
  API_URL: process.env.API_URL!,
  APPLY_TAX: process.env.APPLY_TAX!,
  // Add Google Cloud Storage config (optional)
  GCP_PROJECT_ID: process.env.GCP_PROJECT_ID || 'your_gcp_project_id',
  GCP_CLIENT_EMAIL: process.env.GCP_CLIENT_EMAIL || 'your_service_account@your_project.iam.gserviceaccount.com',
  GCP_PRIVATE_KEY: process.env.GCP_PRIVATE_KEY || '',
  GCP_BUCKET_NAME: process.env.GCP_BUCKET_NAME || 'agentq-test-logs',

  CORE_SERVICE_URL: process.env.CORE_SERVICE_URL!,

  // Device Farm Configuration
  DEVICE_FARM_URL: process.env.DEVICE_FARM_URL || 'http://localhost:4723',
  DEVICE_FARM_USERNAME: process.env.DEVICE_FARM_USERNAME || 'admin',
  DEVICE_FARM_PASSWORD: process.env.DEVICE_FARM_PASSWORD || 'admin',
};