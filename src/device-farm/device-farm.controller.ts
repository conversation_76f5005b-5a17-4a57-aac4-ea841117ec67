import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { DeviceFarmService } from './device-farm.service';

@ApiTags('device-farm')
@ApiBearerAuth()
@Controller('device-farm')
@UseGuards(JwtAuthGuard)
export class DeviceFarmController {
  constructor(private readonly deviceFarmService: DeviceFarmService) {}

  @Get('devices')
  @ApiOperation({ summary: 'Get list of devices from device farm' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of devices from device farm',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'Device ID' },
          name: { type: 'string', description: 'Device name' },
          platform: { type: 'string', description: 'Device platform (android/ios)' },
          status: { type: 'string', description: 'Device status' },
          // Add more properties based on your device farm API response
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 503, description: 'Device farm service unavailable' })
  async getDevices(@Request() req) {
    return this.deviceFarmService.getDevices();
  }

  @Get('health')
  @ApiOperation({ summary: 'Check device farm service health' })
  @ApiResponse({ 
    status: 200, 
    description: 'Device farm service health status',
    schema: {
      type: 'object',
      properties: {
        available: { type: 'boolean', description: 'Whether the service is available' },
        message: { type: 'string', description: 'Health status message' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async checkHealth(@Request() req) {
    return this.deviceFarmService.checkHealth();
  }
}
