import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import axios from 'axios';
import { Config } from '../config';

@Injectable()
export class DeviceFarmService {
  private readonly logger = new Logger(DeviceFarmService.name);
  private readonly deviceFarmUrl: string;
  private readonly username: string;
  private readonly password: string;

  constructor() {
    this.deviceFarmUrl = Config.DEVICE_FARM_URL;
    this.username = Config.DEVICE_FARM_USERNAME;
    this.password = Config.DEVICE_FARM_PASSWORD;
  }

  /**
   * Authenticate with device farm and get access token
   */
  private async authenticate(): Promise<string> {
    try {
      this.logger.log('Authenticating with device farm...');
      
      const authResponse = await axios.post(
        `${this.deviceFarmUrl}/device-farm/api/auth/login`,
        {
          username: this.username,
          password: this.password,
        },
        {
          timeout: 10000, // 10 second timeout
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!authResponse.data?.token) {
        throw new Error('No token received from device farm authentication');
      }

      this.logger.log('Successfully authenticated with device farm');
      return authResponse.data.token;
    } catch (error) {
      this.logger.error('Device farm authentication failed:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          throw new HttpException(
            'Device farm service is not available (connection refused)',
            HttpStatus.SERVICE_UNAVAILABLE
          );
        } else if (error.response?.status === 401) {
          throw new HttpException(
            'Invalid device farm credentials',
            HttpStatus.UNAUTHORIZED
          );
        } else if (error.response) {
          throw new HttpException(
            `Device farm authentication error: ${error.response.status}`,
            HttpStatus.BAD_GATEWAY
          );
        }
      }

      throw new HttpException(
        'Device farm authentication failed',
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  /**
   * Get list of devices from device farm
   */
  async getDevices(): Promise<any> {
    try {
      // 1. Authenticate with device farm
      const token = await this.authenticate();

      // 2. Fetch devices using token
      this.logger.log('Fetching devices from device farm...');
      
      const devicesResponse = await axios.get(
        `${this.deviceFarmUrl}/device-farm/api/device`,
        {
          headers: { 
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000, // 15 second timeout
        }
      );

      this.logger.log(`Successfully fetched ${devicesResponse.data?.length || 0} devices from device farm`);
      return devicesResponse.data;
    } catch (error) {
      this.logger.error('Failed to fetch devices from device farm:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          throw new HttpException(
            'Device farm service is not available (connection refused)',
            HttpStatus.SERVICE_UNAVAILABLE
          );
        } else if (error.response?.status === 401) {
          throw new HttpException(
            'Unauthorized access to device farm',
            HttpStatus.UNAUTHORIZED
          );
        } else if (error.response?.status === 403) {
          throw new HttpException(
            'Forbidden access to device farm',
            HttpStatus.FORBIDDEN
          );
        } else if (error.response) {
          throw new HttpException(
            `Device farm API error: ${error.response.status}`,
            HttpStatus.BAD_GATEWAY
          );
        }
      }

      throw new HttpException(
        'Device farm service unavailable',
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  /**
   * Check device farm service health
   */
  async checkHealth(): Promise<{ available: boolean; message: string }> {
    try {
      const response = await axios.get(`${this.deviceFarmUrl}/health`, {
        timeout: 5000, // 5 second timeout
      });

      if (response.status === 200) {
        this.logger.log('Device farm service is healthy');
        return {
          available: true,
          message: 'Device farm service is available and healthy',
        };
      } else {
        this.logger.warn(`Device farm service returned unexpected status: ${response.status}`);
        return {
          available: false,
          message: `Device farm service returned status: ${response.status}`,
        };
      }
    } catch (error) {
      this.logger.error('Device farm health check failed', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            available: false,
            message: 'Device farm service is not available (connection refused)',
          };
        } else if (error.response) {
          return {
            available: false,
            message: `Device farm service error: ${error.response.status}`,
          };
        }
      }

      return {
        available: false,
        message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}
