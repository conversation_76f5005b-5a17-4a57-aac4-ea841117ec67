import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';
import { MetricsService } from './metrics.service';

@Injectable()
export class DatabaseMetricsService implements OnModuleInit {
  private metricsUpdateInterval: NodeJS.Timeout;

  constructor(
    @InjectConnection() private connection: Connection,
    private readonly metricsService: MetricsService,
  ) {}

  onModuleInit() {
    // Start collecting database metrics every 30 seconds
    this.startMetricsCollection();
  }

  private startMetricsCollection() {
    // Initial collection
    this.collectDatabaseMetrics();

    // Set up interval for regular collection
    this.metricsUpdateInterval = setInterval(() => {
      this.collectDatabaseMetrics();
    }, 30000); // Every 30 seconds
  }

  private async collectDatabaseMetrics() {
    try {
      // Get connection pool metrics
      await this.collectConnectionPoolMetrics();
      
      // Get database performance metrics
      await this.collectDatabasePerformanceMetrics();
    } catch (error) {
      console.error('Error collecting database metrics:', error);
    }
  }

  private async collectConnectionPoolMetrics() {
    try {
      // Get active connections
      const activeConnectionsResult = await this.connection.query(
        "SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'"
      );
      const activeConnections = parseInt(activeConnectionsResult[0]?.count || '0', 10);

      // Get idle connections
      const idleConnectionsResult = await this.connection.query(
        "SELECT count(*) as count FROM pg_stat_activity WHERE state = 'idle'"
      );
      const idleConnections = parseInt(idleConnectionsResult[0]?.count || '0', 10);

      // Update Prometheus metrics
      this.metricsService.updateDatabaseConnections(activeConnections, idleConnections);
    } catch (error) {
      console.error('Error collecting connection pool metrics:', error);
    }
  }

  private async collectDatabasePerformanceMetrics() {
    try {
      // Get database statistics
      const dbStatsResult = await this.connection.query(`
        SELECT 
          datname,
          numbackends,
          xact_commit,
          xact_rollback,
          blks_read,
          blks_hit,
          tup_returned,
          tup_fetched,
          tup_inserted,
          tup_updated,
          tup_deleted
        FROM pg_stat_database 
        WHERE datname = current_database()
      `);

      if (dbStatsResult.length > 0) {
        const stats = dbStatsResult[0];
        
        // Calculate cache hit ratio
        const totalReads = parseInt(stats.blks_read) + parseInt(stats.blks_hit);
        const cacheHitRatio = totalReads > 0 ? (parseInt(stats.blks_hit) / totalReads) * 100 : 0;

        // You can add more specific metrics here based on your needs
        // For now, we'll track these as custom metrics using the existing registry
        // We'll create a simple gauge for cache hit ratio
        const registry = this.metricsService.getRegistry();
        let cacheHitGauge = registry.getSingleMetric('database_cache_hit_ratio');

        if (!cacheHitGauge) {
          const { Gauge } = require('prom-client');
          cacheHitGauge = new Gauge({
            name: 'database_cache_hit_ratio',
            help: 'Database cache hit ratio percentage',
            registers: [registry],
          });
        }

        (cacheHitGauge as any).set(cacheHitRatio);
      }
    } catch (error) {
      console.error('Error collecting database performance metrics:', error);
    }
  }

  // Method to record individual database queries
  async recordQuery<T>(
    operation: string,
    table: string,
    queryFunction: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    let status: 'success' | 'error' = 'success';

    try {
      const result = await queryFunction();
      return result;
    } catch (error) {
      status = 'error';
      throw error;
    } finally {
      const duration = (Date.now() - startTime) / 1000; // Convert to seconds
      this.metricsService.recordDatabaseQuery(operation, table, duration, status);
    }
  }

  // Cleanup method
  onModuleDestroy() {
    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
    }
  }
}
