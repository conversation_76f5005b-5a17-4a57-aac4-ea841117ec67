import { Injectable, OnModuleInit } from '@nestjs/common';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge, Registry } from 'prom-client';

@Injectable()
export class MetricsService implements OnModuleInit {
  private readonly registry: Registry;

  // HTTP Metrics
  public readonly httpRequestsTotal: Counter<string>;
  public readonly httpRequestDuration: Histogram<string>;
  public readonly httpRequestsInProgress: Gauge<string>;

  // Database Metrics
  public readonly databaseConnectionsActive: Gauge<string>;
  public readonly databaseConnectionsIdle: Gauge<string>;
  public readonly databaseQueryDuration: Histogram<string>;
  public readonly databaseQueriesTotal: Counter<string>;

  // Application Metrics
  public readonly applicationInfo: Gauge<string>;
  public readonly applicationUptime: Gauge<string>;

  // Business Metrics
  public readonly usersTotal: Gauge<string>;
  public readonly projectsTotal: Gauge<string>;
  public readonly testRunsTotal: Counter<string>;
  public readonly testCasesTotal: Gauge<string>;
  public readonly apiKeysTotal: Gauge<string>;
  public readonly issuesTotal: Gauge<string>;

  // System Metrics
  public readonly memoryUsage: Gauge<string>;
  public readonly cpuUsage: Gauge<string>;

  constructor() {
    this.registry = register;

    // Initialize HTTP metrics
    this.httpRequestsTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code'],
      registers: [this.registry],
    });

    this.httpRequestDuration = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10],
      registers: [this.registry],
    });

    this.httpRequestsInProgress = new Gauge({
      name: 'http_requests_in_progress',
      help: 'Number of HTTP requests currently being processed',
      labelNames: ['method', 'route'],
      registers: [this.registry],
    });

    // Initialize Database metrics
    this.databaseConnectionsActive = new Gauge({
      name: 'database_connections_active',
      help: 'Number of active database connections',
      registers: [this.registry],
    });

    this.databaseConnectionsIdle = new Gauge({
      name: 'database_connections_idle',
      help: 'Number of idle database connections',
      registers: [this.registry],
    });

    this.databaseQueryDuration = new Histogram({
      name: 'database_query_duration_seconds',
      help: 'Duration of database queries in seconds',
      labelNames: ['operation', 'table'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5],
      registers: [this.registry],
    });

    this.databaseQueriesTotal = new Counter({
      name: 'database_queries_total',
      help: 'Total number of database queries',
      labelNames: ['operation', 'table', 'status'],
      registers: [this.registry],
    });

    // Initialize Application metrics
    this.applicationInfo = new Gauge({
      name: 'application_info',
      help: 'Application information',
      labelNames: ['version', 'name'],
      registers: [this.registry],
    });

    this.applicationUptime = new Gauge({
      name: 'application_uptime_seconds',
      help: 'Application uptime in seconds',
      registers: [this.registry],
    });

    // Initialize Business metrics
    this.usersTotal = new Gauge({
      name: 'users_total',
      help: 'Total number of users',
      registers: [this.registry],
    });

    this.projectsTotal = new Gauge({
      name: 'projects_total',
      help: 'Total number of projects',
      registers: [this.registry],
    });

    this.testRunsTotal = new Counter({
      name: 'test_runs_total',
      help: 'Total number of test runs',
      labelNames: ['status', 'project_id'],
      registers: [this.registry],
    });

    this.testCasesTotal = new Gauge({
      name: 'test_cases_total',
      help: 'Total number of test cases',
      labelNames: ['project_id'],
      registers: [this.registry],
    });

    this.apiKeysTotal = new Gauge({
      name: 'api_keys_total',
      help: 'Total number of API keys',
      labelNames: ['status'],
      registers: [this.registry],
    });

    this.issuesTotal = new Gauge({
      name: 'issues_total',
      help: 'Total number of issues',
      labelNames: ['status', 'severity'],
      registers: [this.registry],
    });

    // Initialize System metrics
    this.memoryUsage = new Gauge({
      name: 'memory_usage_bytes',
      help: 'Memory usage in bytes',
      labelNames: ['type'],
      registers: [this.registry],
    });

    this.cpuUsage = new Gauge({
      name: 'cpu_usage_percent',
      help: 'CPU usage percentage',
      labelNames: ['type'],
      registers: [this.registry],
    });
  }

  onModuleInit() {
    // Collect default metrics (CPU, memory, etc.)
    collectDefaultMetrics({ register: this.registry });

    // Set application info
    this.applicationInfo.set({ version: '1.0.0', name: 'app_backend_agentq' }, 1);

    // Start uptime tracking
    this.startUptimeTracking();
  }

  private startUptimeTracking() {
    const startTime = Date.now();
    setInterval(() => {
      const uptimeSeconds = (Date.now() - startTime) / 1000;
      this.applicationUptime.set(uptimeSeconds);
    }, 5000); // Update every 5 seconds
  }

  // HTTP Metrics helpers
  recordHttpRequest(method: string, route: string, statusCode: number, duration: number) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode.toString() });
    this.httpRequestDuration.observe({ method, route, status_code: statusCode.toString() }, duration);
  }

  incrementHttpRequestsInProgress(method: string, route: string) {
    this.httpRequestsInProgress.inc({ method, route });
  }

  decrementHttpRequestsInProgress(method: string, route: string) {
    this.httpRequestsInProgress.dec({ method, route });
  }

  // Database Metrics helpers
  recordDatabaseQuery(operation: string, table: string, duration: number, status: 'success' | 'error') {
    this.databaseQueriesTotal.inc({ operation, table, status });
    this.databaseQueryDuration.observe({ operation, table }, duration);
  }

  updateDatabaseConnections(active: number, idle: number) {
    this.databaseConnectionsActive.set(active);
    this.databaseConnectionsIdle.set(idle);
  }

  // Business Metrics helpers
  updateUsersCount(count: number) {
    this.usersTotal.set(count);
  }

  updateProjectsCount(count: number) {
    this.projectsTotal.set(count);
  }

  recordTestRun(status: string, projectId: string) {
    this.testRunsTotal.inc({ status, project_id: projectId });
  }

  updateTestCasesCount(projectId: string, count: number) {
    this.testCasesTotal.set({ project_id: projectId }, count);
  }

  updateApiKeysCount(status: string, count: number) {
    this.apiKeysTotal.set({ status }, count);
  }

  updateIssuesCount(status: string, severity: string, count: number) {
    this.issuesTotal.set({ status, severity }, count);
  }

  // System Metrics helpers
  updateMemoryUsage(heapUsed: number, heapTotal: number, rss: number) {
    this.memoryUsage.set({ type: 'heap_used' }, heapUsed);
    this.memoryUsage.set({ type: 'heap_total' }, heapTotal);
    this.memoryUsage.set({ type: 'rss' }, rss);
  }

  updateCpuUsage(system: number, process: number) {
    this.cpuUsage.set({ type: 'system' }, system);
    this.cpuUsage.set({ type: 'process' }, process);
  }

  // Get metrics in Prometheus format
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  // Get registry for custom metrics
  getRegistry(): Registry {
    return this.registry;
  }
}
