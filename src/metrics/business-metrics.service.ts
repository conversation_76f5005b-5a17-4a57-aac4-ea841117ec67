import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';
import { MetricsService } from './metrics.service';

@Injectable()
export class BusinessMetricsService implements OnModuleInit {
  private metricsUpdateInterval: NodeJS.Timeout;

  constructor(
    @InjectConnection() private connection: Connection,
    private readonly metricsService: MetricsService,
  ) {}

  onModuleInit() {
    // Start collecting business metrics every 60 seconds
    this.startMetricsCollection();
  }

  private startMetricsCollection() {
    // Initial collection
    this.collectBusinessMetrics();

    // Set up interval for regular collection
    this.metricsUpdateInterval = setInterval(() => {
      this.collectBusinessMetrics();
    }, 60000); // Every 60 seconds
  }

  private async collectBusinessMetrics() {
    try {
      await Promise.all([
        this.collectUserMetrics(),
        this.collectProjectMetrics(),
        this.collectTestCaseMetrics(),
        this.collectApiKeyMetrics(),
        this.collectIssueMetrics(),
      ]);
    } catch (error) {
      console.error('Error collecting business metrics:', error);
    }
  }

  private async collectUserMetrics() {
    try {
      const userCountResult = await this.connection.query(
        'SELECT COUNT(*) as count FROM users'
      );
      const userCount = parseInt(userCountResult[0]?.count || '0', 10);
      this.metricsService.updateUsersCount(userCount);
    } catch (error) {
      console.error('Error collecting user metrics:', error);
    }
  }

  private async collectProjectMetrics() {
    try {
      const projectCountResult = await this.connection.query(
        'SELECT COUNT(*) as count FROM projects'
      );
      const projectCount = parseInt(projectCountResult[0]?.count || '0', 10);
      this.metricsService.updateProjectsCount(projectCount);
    } catch (error) {
      console.error('Error collecting project metrics:', error);
    }
  }

  private async collectTestCaseMetrics() {
    try {
      // Get test cases count by project
      const testCasesByProjectResult = await this.connection.query(`
        SELECT
          p.id as project_id,
          COUNT(tc.id) as count
        FROM projects p
        LEFT JOIN test_cases tc ON tc."projectId" = p.id
        GROUP BY p.id
      `);

      for (const row of testCasesByProjectResult) {
        const projectId = row.project_id;
        const count = parseInt(row.count || '0', 10);
        this.metricsService.updateTestCasesCount(projectId, count);
      }
    } catch (error) {
      console.error('Error collecting test case metrics:', error);
    }
  }

  private async collectApiKeyMetrics() {
    try {
      // Get API keys count by status (all API keys are considered active if they exist)
      const apiKeyCountResult = await this.connection.query(`
        SELECT
          CASE
            WHEN "createdAt" IS NOT NULL THEN 'active'
            ELSE 'inactive'
          END as status,
          COUNT(*) as count
        FROM api_keys
        GROUP BY status
      `);

      for (const row of apiKeyCountResult) {
        const status = row.status;
        const count = parseInt(row.count || '0', 10);
        this.metricsService.updateApiKeysCount(status, count);
      }
    } catch (error) {
      console.error('Error collecting API key metrics:', error);
    }
  }

  private async collectIssueMetrics() {
    try {
      // Get issues count by status (issues don't have severity in the schema)
      const issueCountResult = await this.connection.query(`
        SELECT
          COALESCE(status, 'unknown') as status,
          'unknown' as severity,
          COUNT(*) as count
        FROM issues
        GROUP BY status
      `);

      for (const row of issueCountResult) {
        const status = row.status;
        const severity = row.severity;
        const count = parseInt(row.count || '0', 10);
        this.metricsService.updateIssuesCount(status, severity, count);
      }
    } catch (error) {
      console.error('Error collecting issue metrics:', error);
    }
  }

  // Methods to record specific business events
  recordUserRegistration() {
    // This would be called when a new user registers
    // The actual count will be updated in the next collection cycle
  }

  recordTestRunStart(projectId: string) {
    this.metricsService.recordTestRun('started', projectId);
  }

  recordTestRunComplete(projectId: string, success: boolean) {
    const status = success ? 'completed' : 'failed';
    this.metricsService.recordTestRun(status, projectId);
  }

  recordApiKeyCreation() {
    // This would be called when a new API key is created
    // The actual count will be updated in the next collection cycle
  }

  recordIssueCreation(severity: string) {
    // This would be called when a new issue is created
    // The actual count will be updated in the next collection cycle
  }

  // Cleanup method
  onModuleDestroy() {
    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
    }
  }
}
