import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { MetricsService } from './metrics.service';

@Injectable()
export class HttpMetricsMiddleware implements NestMiddleware {
  constructor(private readonly metricsService: MetricsService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const method = req.method;
    
    // Get route pattern instead of actual path for better grouping
    // This will be set by NestJS after routing
    let route = req.route?.path || req.path;
    
    // Clean up route for metrics (remove query params, normalize IDs)
    route = this.normalizeRoute(route, req.path);

    // Increment in-progress requests
    this.metricsService.incrementHttpRequestsInProgress(method, route);

    // Override res.end to capture metrics when response is sent
    const originalEnd = res.end.bind(res);
    const metricsService = this.metricsService;

    res.end = function(this: Response, chunk?: any, encoding?: any, cb?: () => void) {
      const duration = (Date.now() - startTime) / 1000; // Convert to seconds
      const statusCode = res.statusCode;

      // Record the HTTP request metrics
      metricsService.recordHttpRequest(method, route, statusCode, duration);

      // Decrement in-progress requests
      metricsService.decrementHttpRequestsInProgress(method, route);

      // Call the original end method
      return originalEnd(chunk, encoding, cb);
    };

    next();
  }

  private normalizeRoute(route: string, actualPath: string): string {
    // If we have the actual route pattern, use it
    if (route && route !== actualPath) {
      return route;
    }

    // Otherwise, normalize the actual path
    let normalizedPath = actualPath;

    // Replace UUIDs with :id
    normalizedPath = normalizedPath.replace(
      /\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
      '/:id'
    );

    // Replace numeric IDs with :id
    normalizedPath = normalizedPath.replace(/\/\d+/g, '/:id');

    // Replace other common ID patterns
    normalizedPath = normalizedPath.replace(/\/[a-zA-Z0-9_-]{20,}/g, '/:id');

    return normalizedPath;
  }
}
