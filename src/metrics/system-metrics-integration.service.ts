import { Injectable, OnModuleInit } from '@nestjs/common';
import { MetricsService } from './metrics.service';
import { SystemMetricsService } from '../health/system-metrics.service';

@Injectable()
export class SystemMetricsIntegrationService implements OnModuleInit {
  private metricsUpdateInterval: NodeJS.Timeout;

  constructor(
    private readonly metricsService: MetricsService,
    private readonly systemMetricsService: SystemMetricsService,
  ) {}

  onModuleInit() {
    // Start collecting system metrics every 15 seconds
    this.startMetricsCollection();
  }

  private startMetricsCollection() {
    // Initial collection
    this.collectSystemMetrics();

    // Set up interval for regular collection
    this.metricsUpdateInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 15000); // Every 15 seconds
  }

  private async collectSystemMetrics() {
    try {
      const systemMetrics = await this.systemMetricsService.getSystemMetrics();

      // Update memory metrics
      this.metricsService.updateMemoryUsage(
        systemMetrics.memory.heapUsed * 1024 * 1024, // Convert MB to bytes
        systemMetrics.memory.heapTotal * 1024 * 1024, // Convert MB to bytes
        systemMetrics.memory.rss * 1024 * 1024 // Convert MB to bytes
      );

      // Update CPU metrics
      this.metricsService.updateCpuUsage(
        systemMetrics.cpu.percent,
        systemMetrics.cpu.user + systemMetrics.cpu.system
      );

    } catch (error) {
      console.error('Error collecting system metrics:', error);
    }
  }

  // Cleanup method
  onModuleDestroy() {
    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
    }
  }
}
