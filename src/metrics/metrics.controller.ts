import { <PERSON>, <PERSON>, Header } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiExcludeEndpoint } from '@nestjs/swagger';
import { MetricsService } from './metrics.service';

@ApiTags('metrics')
@Controller('metrics')
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  @Get()
  @Header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
  @ApiExcludeEndpoint() // Exclude from Swagger as it's for Prometheus
  @ApiOperation({ summary: 'Get Prometheus metrics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Prometheus metrics in text format',
    content: {
      'text/plain': {
        schema: {
          type: 'string',
          example: '# HELP http_requests_total Total number of HTTP requests\n# TYPE http_requests_total counter\nhttp_requests_total{method="GET",route="/health",status_code="200"} 1'
        }
      }
    }
  })
  async getMetrics(): Promise<string> {
    return this.metricsService.getMetrics();
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for metrics service' })
  @ApiResponse({ 
    status: 200, 
    description: 'Metrics service is healthy',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-05-01T12:00:00Z' },
        metricsCount: { type: 'number', example: 25 }
      }
    }
  })
  async getMetricsHealth() {
    const metrics = await this.metricsService.getMetrics();
    const metricsLines = metrics.split('\n').filter(line => 
      line.trim() && !line.startsWith('#')
    );

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      metricsCount: metricsLines.length,
    };
  }
}
