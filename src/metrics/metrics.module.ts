import { Module, Global } from '@nestjs/common';
import { MetricsService } from './metrics.service';
import { MetricsController } from './metrics.controller';
import { HttpMetricsMiddleware } from './http-metrics.middleware';
import { DatabaseMetricsService } from './database-metrics.service';
import { BusinessMetricsService } from './business-metrics.service';
import { SystemMetricsIntegrationService } from './system-metrics-integration.service';
import { SystemMetricsService } from '../health/system-metrics.service';

@Global()
@Module({
  providers: [
    MetricsService,
    HttpMetricsMiddleware,
    DatabaseMetricsService,
    BusinessMetricsService,
    SystemMetricsIntegrationService,
    SystemMetricsService,
  ],
  controllers: [MetricsController],
  exports: [
    MetricsService,
    HttpMetricsMiddleware,
    DatabaseMetricsService,
    BusinessMetricsService,
    SystemMetricsIntegrationService,
  ],
})
export class MetricsModule {}
